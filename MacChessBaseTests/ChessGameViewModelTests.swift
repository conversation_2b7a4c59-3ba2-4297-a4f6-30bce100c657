//
//  ChessGameViewModelTests.swift
//  MacChessBaseTests
//
//  Tests for the main chess game view model
//

import XCTest
@testable import MacChessBase
@testable import ChessKit

final class ChessGameViewModelTests: XCTestCase {
    
    var viewModel: ChessGameViewModel!
    var gameSession: GameSession!
    
    @MainActor
    override func setUp() {
        super.setUp()
        gameSession = GameSession()
        viewModel = ChessGameViewModel(session: gameSession)
    }
    
    override func tearDown() {
        viewModel = nil
        gameSession = nil
        super.tearDown()
    }
    
    // MARK: - Initialization Tests
    
    @MainActor
    func testInitialState() {
        XCTAssertEqual(gameSession.game.startingPosition, Position.standard)
        XCTAssertEqual(viewModel.session.currentMoveIndex, MoveTree.minimumIndex)
        XCTAssertEqual(viewModel.session.currentAnnotationColor, .green)
        XCTAssertFalse(viewModel.session.isBoardFlipped)
        XCTAssertNil(viewModel.selectedSquare)
        XCTAssertNil(viewModel.lastMove)
        XCTAssertTrue(viewModel.possibleMoves.isEmpty)
        XCTAssertEqual(viewModel.gameStatus, .inProgress)
        XCTAssertNil(viewModel.promotionMove)
        XCTAssertFalse(viewModel.showPromotionDialog)
        XCTAssertFalse(viewModel.showVariationSelection)
        XCTAssertFalse(viewModel.showVariationCreationDialog)
        XCTAssertFalse(viewModel.showMoveEditMenu)
        XCTAssertFalse(viewModel.showPositionEditor)
        XCTAssertFalse(viewModel.showImportErrorAlert)
        XCTAssertFalse(viewModel.isReverseDragActive)
        XCTAssertNil(viewModel.reverseDragTarget)
        XCTAssertTrue(viewModel.reverseDragValidSources.isEmpty)
        XCTAssertNil(viewModel.session.currentFilePath)
    }
    
    // MARK: - Move Making Tests
    
    @MainActor
    func testMakeValidMove() {
        // Test making a valid move through the view model
        
        // Select a square and make a move
        viewModel.setSelectedSquare(Square.e2)
        XCTAssertEqual(viewModel.selectedSquare, Square.e2)
        XCTAssertFalse(viewModel.possibleMoves.isEmpty)

        // Attempt to make a move
        viewModel.attemptMove(from: Square.e2, to: Square.e4)
        
        // Check that the move was made
        let piece = viewModel.session.board.position.piece(at: Square.e4)
        XCTAssertEqual(piece?.kind, .pawn)
        XCTAssertEqual(piece?.color, .white)
        XCTAssertNil(viewModel.session.board.position.piece(at: Square.e2))
        XCTAssertEqual(viewModel.session.board.position.sideToMove, .black)
        
        // Check that selection was cleared
        XCTAssertNil(viewModel.selectedSquare)
        XCTAssertTrue(viewModel.possibleMoves.isEmpty)
    }
    
    @MainActor
    func testMakeInvalidMove() {
        let initialPosition = viewModel.session.board.position

        // Try to make an invalid move
        viewModel.attemptMove(from: Square.e2, to: Square.e5)

        // Position should remain unchanged
        XCTAssertEqual(viewModel.session.board.position.fen, initialPosition.fen)
        XCTAssertEqual(viewModel.session.board.position.sideToMove, .white)
    }
    
    @MainActor
    func testSquareSelection() {
        // Test selecting a square with a white pawn
        viewModel.setSelectedSquare(Square.e2)

        XCTAssertEqual(viewModel.selectedSquare, Square.e2)
        XCTAssertFalse(viewModel.possibleMoves.isEmpty)
        XCTAssertTrue(viewModel.possibleMoves.contains(Square.e3))
        XCTAssertTrue(viewModel.possibleMoves.contains(Square.e4))
    }
    
    @MainActor
    func testSquareSelectionClearing() {
        // Select a square
        viewModel.setSelectedSquare(Square.e2)
        XCTAssertNotNil(viewModel.selectedSquare)

        // Clear selection
        viewModel.clearSelection()
        XCTAssertNil(viewModel.selectedSquare)
        XCTAssertTrue(viewModel.possibleMoves.isEmpty)
    }
    
    @MainActor
    func testCanMovePiece() {
        // Test that pieces can be moved
        XCTAssertTrue(viewModel.canMovePiece(at: Square.e2))
        XCTAssertTrue(viewModel.canMovePiece(at: Square.b1))
        XCTAssertTrue(viewModel.canMovePiece(at: Square.g1))
        
        // Test that empty squares can't be moved
        XCTAssertFalse(viewModel.canMovePiece(at: Square.e4))
        XCTAssertFalse(viewModel.canMovePiece(at: Square.d5))
        
        // Test that opponent pieces can't be moved
        XCTAssertFalse(viewModel.canMovePiece(at: Square.e7))
        XCTAssertFalse(viewModel.canMovePiece(at: .d7))
    }
    
    @MainActor
    func testMoveNavigation() async {
        // Make some moves
        viewModel.attemptMove(from: Square.e2, to: Square.e4)
        viewModel.attemptMove(from: Square.e7, to: Square.e5)
        viewModel.attemptMove(from: Square.g1, to: Square.f3)

        let lastIndex = viewModel.session.currentMoveIndex

        // Navigate backward
        viewModel.goToPreviousMove()
        XCTAssertNotEqual(viewModel.session.currentMoveIndex, lastIndex)

        // Navigate forward
        viewModel.goToNextMove()
        XCTAssertEqual(viewModel.session.currentMoveIndex, lastIndex)

        // Navigate to start
        viewModel.goToStart()

        // Wait for async board update to complete
        try? await Task.sleep(nanoseconds: 100_000_000) // 0.1 seconds

        XCTAssertEqual(viewModel.session.currentMoveIndex, MoveTree.minimumIndex)
        XCTAssertEqual(viewModel.session.board.position, Position.standard)

        // Navigate to end
        viewModel.goToEnd()

        // Wait for async board update to complete
        try? await Task.sleep(nanoseconds: 100_000_000) // 0.1 seconds

        XCTAssertEqual(viewModel.session.currentMoveIndex, lastIndex)
    }
    
    @MainActor
    func testNavigationCapabilities() async {
        // At start position
        XCTAssertFalse(viewModel.canGoToPreviousMove)
        XCTAssertFalse(viewModel.canGoToNextMove)
        
        // Make a move
        viewModel.attemptMove(from: Square.e2, to: Square.e4)

        // Now we can go back but not forward
        XCTAssertTrue(viewModel.canGoToPreviousMove)
        XCTAssertFalse(viewModel.canGoToNextMove)

        // Go back to start
        viewModel.goToPreviousMove()

        // Wait for async board update to complete
        try? await Task.sleep(nanoseconds: 100_000_000) // 0.1 seconds

        // Now we can go forward but not back
        XCTAssertFalse(viewModel.canGoToPreviousMove)
        XCTAssertTrue(viewModel.canGoToNextMove)
    }
    
    @MainActor
    func testLoadGameFromPGN() {
        let testPGN = """
        [Event "Test Game"]
        [Site "Test Site"]
        [Date "2023.01.01"]
        [White "Player1"]
        [Black "Player2"]
        [Result "1-0"]
        
        1. e4 e5 2. Nf3 Nc6 3. Bc4 Bc5
        """
        
        viewModel.loadGame(from: testPGN)
        
        XCTAssertEqual(gameSession.game.tags.event, "Test Game")
        XCTAssertEqual(gameSession.game.tags.white, "Player1")
        XCTAssertEqual(gameSession.game.tags.black, "Player2")
        XCTAssertFalse(gameSession.game.moves.isEmpty)
    }
    
    @MainActor
    func testLoadPositionFromFEN() {
        let testFEN = "rnbqkbnr/pppppppp/8/8/4P3/8/PPPP1PPP/RNBQKBNR b KQkq e3 0 1"
        
        viewModel.loadPosition(from: testFEN)
        
        XCTAssertEqual(viewModel.session.board.position.fen, testFEN)
        XCTAssertEqual(viewModel.session.board.position.sideToMove, .black)
        XCTAssertEqual(viewModel.session.board.position.piece(at: Square.e4)?.kind, .pawn)
    }
    
    @MainActor
    func testToggleBoardFlip() async {
        XCTAssertFalse(viewModel.session.isBoardFlipped)

        viewModel.toggleBoardFlip()

        // Wait for the async dispatch to complete
        try? await Task.sleep(nanoseconds: 100_000_000) // 0.1 seconds

        XCTAssertTrue(viewModel.session.isBoardFlipped)

        viewModel.toggleBoardFlip()

        // Wait for the async dispatch to complete
        try? await Task.sleep(nanoseconds: 100_000_000) // 0.1 seconds

        XCTAssertFalse(viewModel.session.isBoardFlipped)
    }
    
    @MainActor
    func testNewGame() {
        // Make some moves
        viewModel.attemptMove(from: Square.e2, to: Square.e4)
        viewModel.attemptMove(from: Square.e7, to: Square.e5)
        
        // Start new game
        viewModel.newGame()
        
        XCTAssertEqual(viewModel.session.board.position, Position.standard)
        XCTAssertEqual(viewModel.session.currentMoveIndex, MoveTree.minimumIndex)
        XCTAssertTrue(gameSession.game.moves.isEmpty)
        XCTAssertNil(viewModel.selectedSquare)
        XCTAssertNil(viewModel.lastMove)
        XCTAssertEqual(viewModel.gameStatus, .inProgress)
        XCTAssertNil(viewModel.session.currentFilePath)
    }
    
    @MainActor
    func testCurrentPlayer() {
        XCTAssertEqual(viewModel.currentPlayer, .white)

        // Make a move
        viewModel.attemptMove(from: Square.e2, to: Square.e4)
        XCTAssertEqual(viewModel.currentPlayer, .black)
    }
    
    @MainActor
    func testPGNGeneration() {
        // Make some moves
        viewModel.attemptMove(from: Square.e2, to: Square.e4)
        viewModel.attemptMove(from: Square.e7, to: Square.e5)
        
        let pgn = viewModel.pgn
        XCTAssertTrue(pgn.contains("1. e4 e5"))
    }
    
    @MainActor
    func testCurrentMoveNumber() {
        XCTAssertEqual(viewModel.currentMoveNumber, "Start")
        
        // Make a move
        viewModel.attemptMove(from: Square.e2, to: Square.e4)
        XCTAssertEqual(viewModel.currentMoveNumber, "1.")
        
        // Make another move
        viewModel.attemptMove(from: Square.e7, to: Square.e5)
        XCTAssertEqual(viewModel.currentMoveNumber, "1…")
    }
    
    @MainActor
    func testAnnotationColor() async {
        XCTAssertEqual(viewModel.session.currentAnnotationColor, .green)

        viewModel.setAnnotationColor(.red)

        // Wait for the async dispatch to complete
        try? await Task.sleep(nanoseconds: 100_000_000) // 0.1 seconds

        XCTAssertEqual(viewModel.session.currentAnnotationColor, .red)

        viewModel.setAnnotationColor(.blue)

        // Wait for the async dispatch to complete
        try? await Task.sleep(nanoseconds: 100_000_000) // 0.1 seconds

        XCTAssertEqual(viewModel.session.currentAnnotationColor, .blue)
    }
    
    @MainActor
    func testReverseDragOperations() {
        // Test starting reverse drag
        let success = viewModel.startReverseDrag(from: Square.e4)
        XCTAssertTrue(success)
        XCTAssertTrue(viewModel.isReverseDragActive)
        XCTAssertEqual(viewModel.reverseDragTarget, Square.e4)
        XCTAssertFalse(viewModel.reverseDragValidSources.isEmpty)
        
        // Test canceling reverse drag
        viewModel.cancelReverseDrag()
        XCTAssertFalse(viewModel.isReverseDragActive)
        XCTAssertNil(viewModel.reverseDragTarget)
        XCTAssertTrue(viewModel.reverseDragValidSources.isEmpty)
    }
    
    @MainActor
    func testGameMetadata() {
        var metadata = gameSession.game.metadata
        metadata.white = "Test White"
        metadata.black = "Test Black"
        metadata.event = "Test Event"
        
        gameSession.game.metadata = metadata
        
        XCTAssertEqual(gameSession.game.tags.white, "Test White")
        XCTAssertEqual(gameSession.game.tags.black, "Test Black")
        XCTAssertEqual(gameSession.game.tags.event, "Test Event")
    }
    
    @MainActor
    func testFormatPlayerInfo() {
        let whiteInfo = viewModel.formatPlayerInfo(name: "Magnus Carlsen", color: .white)
        XCTAssertEqual(whiteInfo, "Magnus Carlsen")

        let blackInfo = viewModel.formatPlayerInfo(name: "", color: .black)
        XCTAssertEqual(blackInfo, "Black")
    }
    
    @MainActor
    func testCachedMoves() {
        // Make some moves
        viewModel.attemptMove(from: Square.e2, to: Square.e4)
        viewModel.attemptMove(from: Square.e7, to: Square.e5)

        // Should have cached moves after making moves
        let movesAfterPlaying = viewModel.cachedMoves
        XCTAssertFalse(movesAfterPlaying.isEmpty)

        // Check that moves are properly formatted
        let moveTexts = movesAfterPlaying.compactMap { $0.displayText }
        XCTAssertTrue(moveTexts.contains("1.e4"))
        XCTAssertTrue(moveTexts.contains("e5"))
    }
    
    @MainActor
    func testGameStatusTracking() {
        // Should start in progress
        XCTAssertEqual(viewModel.gameStatus, .inProgress)
        
        // Make some moves - should still be in progress
        viewModel.attemptMove(from: Square.e2, to: Square.e4)
        viewModel.attemptMove(from: Square.e7, to: Square.e5)
        
        XCTAssertEqual(viewModel.gameStatus, .inProgress)
    }
    
    @MainActor
    func testDragValidation() {
        // Test drag start validation
        let whitePawn = Piece(.pawn, color: .white, square: Square.a1)
        let blackPawn = Piece(.pawn, color: .black, square: Square.a1)

        // Should be able to drag white pieces at start
        XCTAssertTrue(viewModel.validateDragStart(piece: whitePawn, from: Square.e2))

        // Should not be able to drag black pieces at start
        XCTAssertFalse(viewModel.validateDragStart(piece: blackPawn, from: Square.e7))
    }

    @MainActor
    func testClickToSelectPiece() {
        // Test that clicking on a piece selects it and shows possible moves
        XCTAssertNil(viewModel.selectedSquare)
        XCTAssertTrue(viewModel.possibleMoves.isEmpty)

        // Click on e2 pawn (white's turn)
        viewModel.handleSquarePress(Square.e2)

        // Should select the square and show possible moves IMMEDIATELY
        XCTAssertEqual(viewModel.selectedSquare, Square.e2, "Square should be selected immediately after click")
        XCTAssertFalse(viewModel.possibleMoves.isEmpty, "Possible moves should be calculated immediately")
        XCTAssertTrue(viewModel.possibleMoves.contains(Square.e3))
        XCTAssertTrue(viewModel.possibleMoves.contains(Square.e4))

        // Test immediate feedback - the dragManager should have the state
        XCTAssertEqual(viewModel.dragManager.selectedSquare, Square.e2, "DragManager should have immediate state")
        XCTAssertFalse(viewModel.dragManager.possibleMoves.isEmpty, "DragManager should have immediate possible moves")

        // Clear selection
        viewModel.clearSelection()
        XCTAssertNil(viewModel.selectedSquare)
        XCTAssertTrue(viewModel.possibleMoves.isEmpty)
    }

    @MainActor
    func testImmediateUIFeedback() {
        // Test that UI feedback is immediate for different scenarios

        // Test 1: Click on valid piece
        viewModel.handleSquarePress(Square.e2)
        XCTAssertEqual(viewModel.selectedSquare, Square.e2)
        XCTAssertFalse(viewModel.possibleMoves.isEmpty)

        // Test 2: Click on another valid piece (should switch selection)
        viewModel.handleSquarePress(Square.d2)
        XCTAssertEqual(viewModel.selectedSquare, Square.d2)
        XCTAssertFalse(viewModel.possibleMoves.isEmpty)
        XCTAssertTrue(viewModel.possibleMoves.contains(Square.d3))
        XCTAssertTrue(viewModel.possibleMoves.contains(Square.d4))

        // Test 3: Click on empty square (should clear selection)
        viewModel.handleSquarePress(Square.e4)
        XCTAssertNil(viewModel.selectedSquare)
        XCTAssertTrue(viewModel.possibleMoves.isEmpty)

        // Test 4: Click on opponent piece (should clear selection)
        viewModel.handleSquarePress(Square.e7)
        XCTAssertNil(viewModel.selectedSquare)
        XCTAssertTrue(viewModel.possibleMoves.isEmpty)
    }
    
    @MainActor
    func testOpenPositionEditor() {
        XCTAssertFalse(viewModel.showPositionEditor)
        
        viewModel.openPositionEditor()
        XCTAssertTrue(viewModel.showPositionEditor)
    }
    
    // MARK: - Overwrite and Delete All After Tests
    
    @MainActor
    func testOverwriteFunctionality() async {
        // Set up initial game: 1. e4 e5 2. Nf3 Nc6 3. Bc4
        viewModel.attemptMove(from: Square.e2, to: Square.e4)
        viewModel.attemptMove(from: Square.e7, to: Square.e5)
        viewModel.attemptMove(from: Square.g1, to: Square.f3)
        viewModel.attemptMove(from: Square.b8, to: Square.c6)
        viewModel.attemptMove(from: Square.f1, to: Square.c4)
        
        // Go back to after e5
        let indices = gameSession.game.moves.history(for: gameSession.game.moves.lastMainVariationIndex)
        let e5Index = indices[2]
        viewModel.goToMove(at: e5Index)
        
        // Wait for async board update to complete
        try? await Task.sleep(nanoseconds: 200_000_000) // 0.2 seconds
        
        // Test overwrite by playing d3 - this should trigger variation dialog
        viewModel.attemptMove(from: Square.d2, to: Square.d3)
        
        // Now select overwrite option to complete the move
        viewModel.selectVariationCreationOption(.overwrite)
        
        // Wait for async operations to complete
        try? await Task.sleep(nanoseconds: 200_000_000) // 0.2 seconds
        
        // Should have overwritten the line: now it should be 1. e4 e5 2. d3
        XCTAssertEqual(gameSession.game.moves.count, 3) // e4 + e5 + d3
        
        // Verify the new move exists
        let newIndices = gameSession.game.moves.history(for: gameSession.game.moves.lastMainVariationIndex)
        let lastMove = gameSession.game.moves.getNodeMove(index: newIndices.last!)
        XCTAssertEqual(lastMove?.metaMove?.san, "d3")
        
        // Verify old moves (Nf3, Nc6, Bc4) are gone
        let allMoves = gameSession.game.moves.indices.compactMap { 
            gameSession.game.moves.getNodeMove(index: $0)?.metaMove?.san 
        }
        XCTAssertFalse(allMoves.contains("Nf3"))
        XCTAssertFalse(allMoves.contains("Nc6"))
        XCTAssertFalse(allMoves.contains("Bc4"))
    }
    
    @MainActor
    func testOverwriteWithVariations() async {
        // Set up game with variations: 1. e4 e5 2. Nf3 (2. Nc3 Nf6) 2... Nc6
        viewModel.attemptMove(from: Square.e2, to: Square.e4)
        viewModel.attemptMove(from: Square.e7, to: Square.e5)
        viewModel.attemptMove(from: Square.g1, to: Square.f3)
        viewModel.attemptMove(from: Square.b8, to: Square.c6)
        
        // Go back to after e5 and add a variation
        let indices = gameSession.game.moves.history(for: gameSession.game.moves.lastMainVariationIndex)
        let e5Index = indices[2]
        viewModel.goToMove(at: e5Index)
        
        // Wait for async board update to complete
        try? await Task.sleep(nanoseconds: 200_000_000) // 0.2 seconds
        
        // Add variation 2. Nc3 Nf6
        viewModel.attemptMove(from: Square.b1, to: Square.c3)
        viewModel.selectVariationCreationOption(.newVariation)
        
        // Wait for async operations to complete
        try? await Task.sleep(nanoseconds: 200_000_000) // 0.2 seconds
        
        viewModel.attemptMove(from: Square.g8, to: Square.f6)
        
        // Wait for async operations to complete
        try? await Task.sleep(nanoseconds: 200_000_000) // 0.2 seconds
        
        // Go back to after e5 and test overwrite
        viewModel.goToMove(at: e5Index)
        
        // Wait for async board update to complete
        try? await Task.sleep(nanoseconds: 200_000_000) // 0.2 seconds
        
        viewModel.attemptMove(from: Square.d2, to: Square.d3)
        viewModel.selectVariationCreationOption(.overwrite)
        
        // Wait for async operations to complete
        try? await Task.sleep(nanoseconds: 200_000_000) // 0.2 seconds
        
        // Should have overwritten everything after e5 (main line and variations)
        XCTAssertEqual(gameSession.game.moves.count, 3) // e4 + e5 + d3
        
        // Verify no variations exist after e5
        XCTAssertEqual(gameSession.game.moves.variations(from: e5Index).count, 0)
        
        // Verify new move exists
        let newIndices = gameSession.game.moves.history(for: gameSession.game.moves.lastMainVariationIndex)
        let lastMove = gameSession.game.moves.getNodeMove(index: newIndices.last!)
        XCTAssertEqual(lastMove?.metaMove?.san, "d3")
    }
    
    @MainActor
    func testOverwriteFromVariation() async {
        // Set up: 1. e4 e5 2. Nf3 Nc6 3. Bc4 (3. Bb5 a6)
        viewModel.attemptMove(from: Square.e2, to: Square.e4)
        viewModel.attemptMove(from: Square.e7, to: Square.e5)
        viewModel.attemptMove(from: Square.g1, to: Square.f3)
        viewModel.attemptMove(from: Square.b8, to: Square.c6)
        viewModel.attemptMove(from: Square.f1, to: Square.c4)
        
        // Add variation 3. Bb5 a6
        let indices = gameSession.game.moves.history(for: gameSession.game.moves.lastMainVariationIndex)
        let nc6Index = indices[4]
        viewModel.goToMove(at: nc6Index)
        
        // Wait for async board update to complete
        try? await Task.sleep(nanoseconds: 200_000_000) // 0.2 seconds
        
        viewModel.attemptMove(from: Square.f1, to: Square.b5)
        viewModel.selectVariationCreationOption(.newVariation)
        
        // Wait for async operations to complete
        try? await Task.sleep(nanoseconds: 200_000_000) // 0.2 seconds
        
        viewModel.attemptMove(from: .a7, to: Square.a6)
        
        // Go back to after Bb5 and overwrite
        let variationIndices = gameSession.game.moves.variations(from: nc6Index)
        let bb5Index = variationIndices[0]
        viewModel.goToMove(at: bb5Index)
        
        // Wait for async board update to complete
        try? await Task.sleep(nanoseconds: 200_000_000) // 0.2 seconds
        
        viewModel.attemptMove(from: Square.d2, to: Square.d3)
        viewModel.selectVariationCreationOption(.overwrite)
        
        // Wait for async operations to complete
        try? await Task.sleep(nanoseconds: 200_000_000) // 0.2 seconds
        
        // Should have overwritten a6 in the variation
        let bb5Move = gameSession.game.moves.getNodeMove(index: bb5Index)
        XCTAssertEqual(bb5Move?.metaMove?.san, "Bb5")
        
        // a6 should be gone, d3 should be the new continuation
        let nextIndex = gameSession.game.moves.nextIndex(currentIndex: bb5Index)
        let nextMove = gameSession.game.moves.getNodeMove(index: nextIndex!)
        XCTAssertEqual(nextMove?.metaMove?.san, "d3")
        
        // Main line should remain intact
        XCTAssertNotNil(gameSession.game.moves.getNodeMove(index: indices[5])) // Bc4
    }
    
    @MainActor
    func testOverwriteVariationOptionReset() async {
        // Test that variation option resets to default after overwrite
        viewModel.attemptMove(from: Square.e2, to: Square.e4)
        viewModel.attemptMove(from: Square.e7, to: Square.e5)
        
        // Set overwrite option and make a move
        viewModel.goToStart()
        viewModel.attemptMove(from: Square.d2, to: Square.d3)
        viewModel.selectVariationCreationOption(.overwrite)
        
        // Wait for async operations to complete
        try? await Task.sleep(nanoseconds: 200_000_000) // 0.2 seconds
        
        // Variation option should remain as set (doesn't auto-reset)
        XCTAssertEqual(viewModel.lastMove?.metaMove?.san, "d3")
    }
    
    @MainActor
    func testDeleteAllAfterThroughViewModel() {
        // Test that deleteAllAfter works through the view model
        viewModel.attemptMove(from: Square.e2, to: Square.e4)
        viewModel.attemptMove(from: Square.e7, to: Square.e5)
        viewModel.attemptMove(from: Square.g1, to: Square.f3)
        viewModel.attemptMove(from: Square.b8, to: Square.c6)
        
        let indices = gameSession.game.moves.history(for: gameSession.game.moves.lastMainVariationIndex)
        let e5Index = indices[2]
        
        // Delete all after e5
        let success = gameSession.game.deleteAllAfter(index: e5Index)
        
        XCTAssertTrue(success)
        XCTAssertEqual(gameSession.game.moves.count, 2) // Only e4 and e5 remain
        
        // Test that the view model state is properly updated
        // Access cachedMoves to trigger cache rebuild
        let cachedMoves = viewModel.cachedMoves
        let moveTexts = cachedMoves.compactMap { $0.displayText }
        XCTAssertTrue(moveTexts.contains("1.e4"))
        XCTAssertTrue(moveTexts.contains("e5"))
        XCTAssertFalse(moveTexts.contains("2.Nf3"))
        XCTAssertFalse(moveTexts.contains("Nc6"))
    }
    
    @MainActor
    func testOverwritePositionUpdates() async {
        // Test that positions are properly updated after overwrite
        viewModel.attemptMove(from: Square.e2, to: Square.e4)
        viewModel.attemptMove(from: Square.e7, to: Square.e5)
        viewModel.attemptMove(from: Square.g1, to: Square.f3)
        
        let indices = gameSession.game.moves.history(for: gameSession.game.moves.lastMainVariationIndex)
        let e5Index = indices[2]
        let nf3Index = indices[3]
        
        // Verify position after Nf3 exists
        XCTAssertNotNil(gameSession.game.positions[nf3Index])
        
        // Go back to after e5 and overwrite
        viewModel.goToMove(at: e5Index)
        
        // Wait for async board update to complete
        try? await Task.sleep(nanoseconds: 200_000_000) // 0.2 seconds
        
        viewModel.attemptMove(from: Square.d2, to: Square.d3)
        viewModel.selectVariationCreationOption(.overwrite)
        
        // Wait for async operations to complete
        try? await Task.sleep(nanoseconds: 200_000_000) // 0.2 seconds
        
        // Position after original Nf3 should be gone
        XCTAssertNil(gameSession.game.positions[nf3Index])
        
        // New position after d3 should exist
        let newIndices = gameSession.game.moves.history(for: gameSession.game.moves.lastMainVariationIndex)
        let d3Index = newIndices.last!
        XCTAssertNotNil(gameSession.game.positions[d3Index])
    }
    
    @MainActor
    func testMoveCommentOperations() {
        // Make a move first
        viewModel.attemptMove(from: Square.e2, to: Square.e4)
        let moveIndex = viewModel.session.currentMoveIndex
        
        // Test setting comment
        viewModel.setMoveCommentText("Good opening move", at: moveIndex)
        let comment = viewModel.getMoveCommentText(at: moveIndex)
        XCTAssertEqual(comment, "Good opening move")
    }
    
    // MARK: - Variation Creation Tests
    
    @MainActor
    func testCreateNewVariation() async {
        // Set up: 1. e4 e5 2. Nf3
        viewModel.attemptMove(from: Square.e2, to: Square.e4)
        viewModel.attemptMove(from: Square.e7, to: Square.e5)
        viewModel.attemptMove(from: Square.g1, to: Square.f3)
        
        // Go back to after e5 and create a variation
        let indices = gameSession.game.moves.history(for: gameSession.game.moves.lastMainVariationIndex)
        let e5Index = indices[2]
        viewModel.goToMove(at: e5Index)
        
        // Wait for async board update to complete
        try? await Task.sleep(nanoseconds: 200_000_000) // 0.2 seconds
        
        // Create variation: 2. Nc3
        viewModel.attemptMove(from: Square.b1, to: Square.c3)
        viewModel.selectVariationCreationOption(.newVariation)
        
        // Wait for async operations to complete
        try? await Task.sleep(nanoseconds: 200_000_000) // 0.2 seconds
        
        // Should have created a new variation
        let variations = gameSession.game.moves.variations(from: e5Index)
        XCTAssertEqual(variations.count, 1)
        
        // The variation should contain Nc3
        let nc3Index = variations[0]
        let nc3Move = gameSession.game.moves.getNodeMove(index: nc3Index)
        XCTAssertEqual(nc3Move?.metaMove?.san, "Nc3")
        
        // Main line should still exist (Nf3)
        let mainNextIndex = gameSession.game.moves.nextIndex(currentIndex: e5Index)
        let mainNextMove = gameSession.game.moves.getNodeMove(index: mainNextIndex!)
        XCTAssertEqual(mainNextMove?.metaMove?.san, "Nf3")
        
        // Should have both moves in the tree
        XCTAssertEqual(gameSession.game.moves.allCount, 4) // e4 + e5 + Nf3 + Nc3
    }
    
    @MainActor
    func testCreateMultipleVariations() async {
        // Set up: 1. e4 e5 2. Nf3
        viewModel.attemptMove(from: Square.e2, to: Square.e4)
        viewModel.attemptMove(from: Square.e7, to: Square.e5)
        viewModel.attemptMove(from: Square.g1, to: Square.f3)
        
        // Go back to after e5 and create first variation
        let indices = gameSession.game.moves.history(for: gameSession.game.moves.lastMainVariationIndex)
        let e5Index = indices[2]
        viewModel.goToMove(at: e5Index)
        
        // Create first variation: 2. Nc3
        viewModel.attemptMove(from: Square.b1, to: Square.c3)
        viewModel.selectVariationCreationOption(.newVariation)
        
        // Wait for async operations to complete
        try? await Task.sleep(nanoseconds: 200_000_000) // 0.2 seconds
        
        // Go back to after e5 and create second variation
        viewModel.goToMove(at: e5Index)
        
        // Create second variation: 2. Bc4
        viewModel.attemptMove(from: Square.f1, to: Square.c4)
        viewModel.selectVariationCreationOption(.newVariation)
        
        // Wait for async operations to complete
        try? await Task.sleep(nanoseconds: 200_000_000) // 0.2 seconds
        
        // Should have created two variations
        let variations = gameSession.game.moves.variations(from: e5Index)
        XCTAssertEqual(variations.count, 2)
        
        // Get the moves at each variation
        let variation1Move = gameSession.game.moves.getNodeMove(index: variations[0])
        let variation2Move = gameSession.game.moves.getNodeMove(index: variations[1])
        
        // Should have both variation moves (order might vary)
        let variationMoves = [variation1Move?.metaMove?.san, variation2Move?.metaMove?.san]
        XCTAssertTrue(variationMoves.contains("Nc3"))
        XCTAssertTrue(variationMoves.contains("Bc4"))
        
        // Main line should still exist (Nf3)
        let mainNextIndex = gameSession.game.moves.nextIndex(currentIndex: e5Index)
        let mainNextMove = gameSession.game.moves.getNodeMove(index: mainNextIndex!)
        XCTAssertEqual(mainNextMove?.metaMove?.san, "Nf3")
        
        // Should have all moves in the tree
        XCTAssertEqual(gameSession.game.moves.allCount, 5) // e4 + e5 + Nf3 + Nc3 + Bc4
    }
    
    @MainActor
    func testCreateVariationWithContinuation() async {
        // Set up: 1. e4 e5 2. Nf3 Nc6
        viewModel.attemptMove(from: Square.e2, to: Square.e4)
        viewModel.attemptMove(from: Square.e7, to: Square.e5)
        viewModel.attemptMove(from: Square.g1, to: Square.f3)
        viewModel.attemptMove(from: Square.b8, to: Square.c6)
        
        // Go back to after e5 and create a variation with continuation
        let indices = gameSession.game.moves.history(for: gameSession.game.moves.lastMainVariationIndex)
        let e5Index = indices[2]
        viewModel.goToMove(at: e5Index)
        
        // Create variation: 2. Nc3 Nf6
        viewModel.attemptMove(from: Square.b1, to: Square.c3)
        viewModel.selectVariationCreationOption(.newVariation)
        
        // Wait for async operations to complete
        try? await Task.sleep(nanoseconds: 200_000_000) // 0.2 seconds
        
        viewModel.attemptMove(from: Square.g8, to: Square.f6)
        
        // Wait for async operations to complete
        try? await Task.sleep(nanoseconds: 200_000_000) // 0.2 seconds
        
        // Should have created a variation with continuation
        let variations = gameSession.game.moves.variations(from: e5Index)
        XCTAssertEqual(variations.count, 1)
        
        // The variation should contain Nc3
        let nc3Index = variations[0]
        let nc3Move = gameSession.game.moves.getNodeMove(index: nc3Index)
        XCTAssertEqual(nc3Move?.metaMove?.san, "Nc3")
        
        // The variation should continue with Nf6
        let nf6Index = gameSession.game.moves.nextIndex(currentIndex: nc3Index)
        let nf6Move = gameSession.game.moves.getNodeMove(index: nf6Index!)
        XCTAssertEqual(nf6Move?.metaMove?.san, "Nf6")
        
        // Main line should still exist (Nf3 Nc6)
        let mainNextIndex = gameSession.game.moves.nextIndex(currentIndex: e5Index)
        let mainNextMove = gameSession.game.moves.getNodeMove(index: mainNextIndex!)
        XCTAssertEqual(mainNextMove?.metaMove?.san, "Nf3")
        
        // Should have all moves in the tree
        XCTAssertEqual(gameSession.game.moves.allCount, 6) // e4 + e5 + Nf3 + Nc6 + Nc3 + Nf6
    }
    
    @MainActor
    func testCreateVariationFromStart() async {
        // Make an initial move
        viewModel.attemptMove(from: Square.e2, to: Square.e4)
        
        // Go back to start and create a variation
        viewModel.goToStart()
        
        // Create variation: 1. d4
        viewModel.attemptMove(from: Square.d2, to: Square.d4)
        viewModel.selectVariationCreationOption(.newVariation)
        
        // Wait for async operations to complete
        try? await Task.sleep(nanoseconds: 200_000_000) // 0.2 seconds
        
        // Should have created a variation from the start
        let variations = gameSession.game.moves.variations(from: MoveTree.minimumIndex)
        XCTAssertEqual(variations.count, 1)
        
        // The variation should contain d4
        let d4Index = variations[0]
        let d4Move = gameSession.game.moves.getNodeMove(index: d4Index)
        XCTAssertEqual(d4Move?.metaMove?.san, "d4")
        
        // Main line should still exist (e4)
        let mainNextIndex = gameSession.game.moves.nextIndex(currentIndex: MoveTree.minimumIndex)
        let mainNextMove = gameSession.game.moves.getNodeMove(index: mainNextIndex!)
        XCTAssertEqual(mainNextMove?.metaMove?.san, "e4")
        
        // Should have both moves in the tree
        XCTAssertEqual(gameSession.game.moves.allCount, 2) // e4 + d4
    }
    
    // MARK: - New Main Variation Tests
    
    @MainActor
    func testCreateNewMainVariation() async {
        // Set up: 1. e4 e5 2. Nf3
        viewModel.attemptMove(from: Square.e2, to: Square.e4)
        viewModel.attemptMove(from: Square.e7, to: Square.e5)
        viewModel.attemptMove(from: Square.g1, to: Square.f3)
        
        // Go back to after e5 and create a new main variation
        let indices = gameSession.game.moves.history(for: gameSession.game.moves.lastMainVariationIndex)
        let e5Index = indices[2]
        viewModel.goToMove(at: e5Index)
        
        // Create new main variation: 2. Nc3
        viewModel.attemptMove(from: Square.b1, to: Square.c3)
        viewModel.selectVariationCreationOption(.newMainLine)
        
        // Wait for async operations to complete
        try? await Task.sleep(nanoseconds: 200_000_000) // 0.2 seconds
        
        // The new move should be the main line now
        let newMainNextIndex = gameSession.game.moves.nextIndex(currentIndex: e5Index)
        let newMainNextMove = gameSession.game.moves.getNodeMove(index: newMainNextIndex!)
        XCTAssertEqual(newMainNextMove?.metaMove?.san, "Nc3")
        
        // The old main line move should now be a variation
        let variations = gameSession.game.moves.variations(from: e5Index)
        XCTAssertEqual(variations.count, 1)
        
        let oldMainMove = gameSession.game.moves.getNodeMove(index: variations[0])
        XCTAssertEqual(oldMainMove?.metaMove?.san, "Nf3")
        
        // Should have both moves in the tree
        XCTAssertEqual(gameSession.game.moves.allCount, 4) // e4 + e5 + Nc3 + Nf3
    }
    
    @MainActor
    func testCreateNewMainVariationWithContinuation() async {
        // Set up: 1. e4 e5 2. Nf3 Nc6 3. Bc4
        viewModel.attemptMove(from: Square.e2, to: Square.e4)
        viewModel.attemptMove(from: Square.e7, to: Square.e5)
        viewModel.attemptMove(from: Square.g1, to: Square.f3)
        viewModel.attemptMove(from: Square.b8, to: Square.c6)
        viewModel.attemptMove(from: Square.f1, to: Square.c4)
        
        // Go back to after e5 and create a new main variation with continuation
        let indices = gameSession.game.moves.history(for: gameSession.game.moves.lastMainVariationIndex)
        let e5Index = indices[2]
        viewModel.goToMove(at: e5Index)
        
        // Create new main variation: 2. Nc3 Nf6
        viewModel.attemptMove(from: Square.b1, to: Square.c3)
        viewModel.selectVariationCreationOption(.newMainLine)
        
        // Wait for async operations to complete
        try? await Task.sleep(nanoseconds: 200_000_000) // 0.2 seconds
        
        viewModel.attemptMove(from: Square.g8, to: Square.f6)
        
        // Wait for async operations to complete
        try? await Task.sleep(nanoseconds: 200_000_000) // 0.2 seconds
        
        // The new line should be the main line now
        let newMainIndices = gameSession.game.moves.history(for: gameSession.game.moves.lastMainVariationIndex)
        XCTAssertEqual(newMainIndices.count, 5) // head + e4 + e5 + Nc3 + Nf6
        
        let nc3Move = gameSession.game.moves.getNodeMove(index: newMainIndices[3])
        let nf6Move = gameSession.game.moves.getNodeMove(index: newMainIndices[4])
        XCTAssertEqual(nc3Move?.metaMove?.san, "Nc3")
        XCTAssertEqual(nf6Move?.metaMove?.san, "Nf6")
        
        // The old main line moves should now be variations
        let variations = gameSession.game.moves.variations(from: e5Index)
        XCTAssertEqual(variations.count, 1)
        
        let oldMainMove = gameSession.game.moves.getNodeMove(index: variations[0])
        XCTAssertEqual(oldMainMove?.metaMove?.san, "Nf3")
        
        // Should have all moves in the tree
        XCTAssertEqual(gameSession.game.moves.allCount, 7) // e4 + e5 + Nc3 + Nf6 + Nf3 + Nc6 + Bc4
    }
    
    @MainActor
    func testNewMainVariationFromStart() async {
        // Make an initial move
        viewModel.attemptMove(from: Square.e2, to: Square.e4)
        
        // Go back to start and create a new main variation
        viewModel.goToStart()
        
        // Create new main variation: 1. d4
        viewModel.attemptMove(from: Square.d2, to: Square.d4)
        viewModel.selectVariationCreationOption(.newMainLine)
        
        // Wait for async operations to complete
        try? await Task.sleep(nanoseconds: 200_000_000) // 0.2 seconds
        
        // The new move should be the main line now
        let newMainNextIndex = gameSession.game.moves.nextIndex(currentIndex: MoveTree.minimumIndex)
        let newMainNextMove = gameSession.game.moves.getNodeMove(index: newMainNextIndex!)
        XCTAssertEqual(newMainNextMove?.metaMove?.san, "d4")
        
        // The old main line move should now be a variation
        let variations = gameSession.game.moves.variations(from: MoveTree.minimumIndex)
        XCTAssertEqual(variations.count, 1)
        
        let oldMainMove = gameSession.game.moves.getNodeMove(index: variations[0])
        XCTAssertEqual(oldMainMove?.metaMove?.san, "e4")
        
        // Should have both moves in the tree
        XCTAssertEqual(gameSession.game.moves.allCount, 2) // d4 + e4
    }
    
    @MainActor
    func testNewMainVariationWithExistingVariations() async {
        // Set up: 1. e4 e5 2. Nf3 (2. Nc3) 2... Nc6
        viewModel.attemptMove(from: Square.e2, to: Square.e4)
        viewModel.attemptMove(from: Square.e7, to: Square.e5)
        viewModel.attemptMove(from: Square.g1, to: Square.f3)
        viewModel.attemptMove(from: Square.b8, to: Square.c6)
        
        // Go back to after e5 and create a variation first
        let indices = gameSession.game.moves.history(for: gameSession.game.moves.lastMainVariationIndex)
        let e5Index = indices[2]
        viewModel.goToMove(at: e5Index)
        
        // Create first variation: 2. Nc3
        viewModel.attemptMove(from: Square.b1, to: Square.c3)
        viewModel.selectVariationCreationOption(.newVariation)
        
        // Wait for async operations to complete
        try? await Task.sleep(nanoseconds: 200_000_000) // 0.2 seconds
        
        // Go back to after e5 and create a new main variation
        viewModel.goToMove(at: e5Index)
        
        // Create new main variation: 2. Bc4
        viewModel.attemptMove(from: Square.f1, to: Square.c4)
        viewModel.selectVariationCreationOption(.newMainLine)
        
        // Wait for async operations to complete
        try? await Task.sleep(nanoseconds: 200_000_000) // 0.2 seconds
        
        // The new move should be the main line now
        let newMainNextIndex = gameSession.game.moves.nextIndex(currentIndex: e5Index)
        let newMainNextMove = gameSession.game.moves.getNodeMove(index: newMainNextIndex!)
        XCTAssertEqual(newMainNextMove?.metaMove?.san, "Bc4")
        
        // Should have two variations now (old main line and first variation)
        let variations = gameSession.game.moves.variations(from: e5Index)
        XCTAssertEqual(variations.count, 2)
        
        let variation1Move = gameSession.game.moves.getNodeMove(index: variations[0])
        let variation2Move = gameSession.game.moves.getNodeMove(index: variations[1])
        
        // Should have both old moves as variations (order might vary)
        let variationMoves = [variation1Move?.metaMove?.san, variation2Move?.metaMove?.san]
        XCTAssertTrue(variationMoves.contains("Nf3"))
        XCTAssertTrue(variationMoves.contains("Nc3"))
        
        // Should have all moves in the tree
        XCTAssertEqual(gameSession.game.moves.allCount, 6) // e4 + e5 + Bc4 + Nf3 + Nc6 + Nc3
    }
}
