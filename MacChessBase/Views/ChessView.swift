//
//  ChessView.swift
//  MacChessBase
//
//  Created by <PERSON> on 2025/7/26.
//

import SwiftUI
import AppKit

struct ChessView: View {
    @EnvironmentObject private var sessionManager: GameSessionManager
    @Environment(\.undoManager) private var undoManager
    @StateObject private var fileManager = ChessFileManager.shared
    @State private var showPGNMetadataDialog = false
    @State private var shouldSaveAfterMetadataEdit = false
    @State private var showingDeleteAllConfirmation = false
    @FocusState private var isFocused: Bool

    var body: some View {
        NavigationSplitView {
            // Sidebar with game list and options
            sidebar
        } detail: {
            // Main chess interface
            if let activeSession = sessionManager.activeSession {
                let viewModel = sessionManager.getViewModel(for: activeSession)
                ChessGameView(viewModel: viewModel)
                    .onAppear {
                        activeSession.undoManager = undoManager
                    }
                    .onChange(of: sessionManager.activeSession?.id) {
                        sessionManager.activeSession?.undoManager = undoManager
                    }
            } else {
                Text("No active game session")
                    .foregroundColor(.secondary)
            }
        }
        .onReceive(NotificationCenter.default.publisher(for: .newGame)) { _ in
            sessionManager.createNewGame()
        }
        .onReceive(NotificationCenter.default.publisher(for: .setUpPosition)) { _ in
            sessionManager.activeViewModel?.openPositionEditor()
        }
        
        .onReceive(NotificationCenter.default.publisher(for: .openFile)) { _ in
            openFile()
        }
        .onReceive(NotificationCenter.default.publisher(for: .importFromClipboard)) { _ in
            sessionManager.importFromClipboard()
        }
        .onReceive(NotificationCenter.default.publisher(for: .save)) { _ in
            saveGame()
        }
        .onReceive(NotificationCenter.default.publisher(for: .editMetadata)) { _ in
            editMetadata()
        }
        .onReceive(NotificationCenter.default.publisher(for: .saveAs)) { _ in
            saveGameAs()
        }
        .onReceive(NotificationCenter.default.publisher(for: .copyPGN)) { _ in
            copyPGNToClipboard()
        }
        .sheet(isPresented: $showPGNMetadataDialog) {
            if let activeSession = sessionManager.activeSession {
                let viewModel = sessionManager.getViewModel(for: activeSession)
                PGNMetadataView(
                    isPresented: $showPGNMetadataDialog,
                    viewModel: viewModel
                )
                .onDisappear {
                    // Check if we should proceed to save after editing metadata
                    if shouldSaveAfterMetadataEdit {
                        // This was triggered by "Save As", so show save dialog
                        saveGameWithCurrentMetadata()
                        shouldSaveAfterMetadataEdit = false
                    }
                    // If it was just "Edit Metadata", we're done - metadata is already saved
                }
            }
        }
        .alert("Delete All Games", isPresented: $showingDeleteAllConfirmation) {
            Button("Cancel", role: .cancel) { }
            Button("Delete All", role: .destructive) {
                sessionManager.removeAllSessions()
            }
        } message: {
            Text("Are you sure you want to delete all games? This action cannot be undone.")
        }
        .onKeyPress { keyPress in
            handleGlobalKeyPress(keyPress)
        }
        .focusable()
        .focused($isFocused)
        .onAppear {
            isFocused = true
        }
        .frame(minWidth: 1200, minHeight: 600)
    }
    
    /// Handles global keyboard shortcuts for move navigation
    private func handleGlobalKeyPress(_ keyPress: KeyPress) -> KeyPress.Result {
        guard let currentViewModel = sessionManager.activeViewModel else {
            return .ignored
        }
        
        // Handle annotation color change shortcuts
        if keyPress.modifiers.contains(.option) {
            switch keyPress.key {
            case "r", "2":
                currentViewModel.setAnnotationColor(.red)
                return .handled
            case "g", "1":
                currentViewModel.setAnnotationColor(.green)
                return .handled
            case "b", "3":
                currentViewModel.setAnnotationColor(.blue)
                return .handled
            default:
                break
            }
        }

        // If keyboard navigation is disabled (e.g., variation selection dialog is open), ignore
        if currentViewModel.isKeyboardNavigationDisabled {
            return .ignored
        }

        switch keyPress.key {
        case .leftArrow:
            Task { @MainActor in
                if keyPress.modifiers.contains(.command) {
                    currentViewModel.goToStart()
                } else {
                    currentViewModel.goToPreviousMove()
                }
            }
            return .handled

        case .rightArrow:
            Task { @MainActor in
                if keyPress.modifiers.contains(.command) {
                    currentViewModel.goToEnd()
                } else {
                    currentViewModel.goToNextMove()
                }
            }
            return .handled

        default:
            return .ignored
        }
    }
    
    private var sidebar: some View {
        List {
            Section("Game Sessions") {
                // New Game row with Delete All Games button
                HStack {
                    Button(action: {
                        sessionManager.createNewGame()
                    }) {
                        HStack {
                            Image(systemName: "plus.circle.fill")
                            Text("New Game")
                        }
                    }
                    .buttonStyle(.plain)

                    Spacer()

                    Button(action: {
                        showingDeleteAllConfirmation = true
                    }) {
                        Image(systemName: "trash.fill")
                            .foregroundColor(.red)
                    }
                    .buttonStyle(.plain)
                    .help("Delete All Games")
                }
                
                ForEach(sessionManager.sortedSessions) { session in
                    GameSessionRow(
                        session: session,
                        isActive: session.isActive,
                        onSelect: {
                            sessionManager.setActiveSession(session)
                        },
                        onRemove: {
                            sessionManager.removeSession(session)
                        },
                        onAddBefore: {
                            sessionManager.createNewGameBefore(session)
                        },
                        onAddAfter: {
                            sessionManager.createNewGameAfter(session)
                        }
                    )
                }
            }
        }
        .navigationSplitViewColumnWidth(min: 200, ideal: 320, max: 500)
    }
    
    private func openFile() {
        guard let url = fileManager.showOpenDialog() else { return }
        sessionManager.loadGameInNewSession(from: url)
    }
    
    private func saveGame() {
        guard let currentViewModel = sessionManager.activeViewModel else { return }
        
        // If currentFilePath exists, save directly without metadata dialog
        if currentViewModel.session.currentFilePath != nil {
            do {
                try currentViewModel.saveGame()
            } catch {
                print("Error saving PGN file: \(error)")
            }
        } else {
            // If no current file, fall back to Save As (which shows metadata dialog)
            saveGameAs()
        }
    }
    
    
    private func editMetadata() {
        // Just show the metadata dialog for editing, without saving
        shouldSaveAfterMetadataEdit = false
        showPGNMetadataDialog = true
    }
    
    private func saveGameAs() {
        // Show metadata dialog first, then save after editing
        shouldSaveAfterMetadataEdit = true
        showPGNMetadataDialog = true
    }
    
    private func saveGameWithCurrentMetadata() {
        guard let activeSession = sessionManager.activeSession else { return }
        
        // Game already contains updated metadata from direct editing
        fileManager.saveGameWithDialog(activeSession.game)
    }
    
    
    private func copyPGNToClipboard() {
        guard let activeSession = sessionManager.activeSession else { return }
        fileManager.copyGameToClipboard(activeSession.game)
    }
}

/// Row view for displaying a game session in the sidebar
struct GameSessionRow: View {
    @ObservedObject var session: GameSession
    let isActive: Bool
    let onSelect: () -> Void
    let onRemove: () -> Void
    let onAddBefore: () -> Void
    let onAddAfter: () -> Void
    
    @State private var showingRenameAlert = false
    @State private var newName = ""
    
    var body: some View {
        VStack(spacing: 0) {
            HStack(spacing: 8) {
                // Left side: Player names (can be multiline)
                HStack(spacing: 4) {
                    Text(session.playersDisplayText)
                        .font(.headline)
                        .foregroundColor(.primary)
                        .lineLimit(nil) // Allow unlimited lines
                        .multilineTextAlignment(.leading)
                        .fixedSize(horizontal: false, vertical: true) // Allow text to wrap vertically
                        .layoutPriority(1) // Give text priority in layout
                    
                    Spacer(minLength: 8) // Ensure some space between text and result
                }
                
                // Right side: Game result (vertically centered)
                VStack(spacing: 2) {
                    Text(gameResultText)
                        .font(.headline)
                        .fontWeight(.medium)
                        .foregroundColor(.primary)
                        .multilineTextAlignment(.center)
                        .lineLimit(1) // Allow result to wrap to 2 lines if needed
                        .fixedSize(horizontal: false, vertical: true)
                    
                }
                .layoutPriority(0) // Lower priority than text
                
                // Active indicator below result
                if isActive {
                    Circle()
                        .fill(Color.green)
                        .frame(width: 6, height: 6)
                }
                
                // Menu button
                Menu {
                    Button("Add Before") {
                        onAddBefore()
                    }

                    Button("Add After") {
                        onAddAfter()
                    }

                    Divider()

                    Button("Remove", role: .destructive) {
                        onRemove()
                    }
                } label: {
                    Image(systemName: "ellipsis.circle")
                        .foregroundColor(.secondary)
                }
                .menuStyle(.borderlessButton)
                .fixedSize()
            }
            .padding(.vertical, 6) // Slightly more padding for better spacing
        }
        .contentShape(Rectangle())
        .onTapGesture {
            onSelect()
        }
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(isActive ? Color.accentColor.opacity(0.1) : Color.clear)
        )
        .alert("Rename Game", isPresented: $showingRenameAlert) {
            TextField("Game Name", text: $newName)
            Button("Cancel", role: .cancel) { }
            Button("Rename") {
                if !newName.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
                    session.name = newName.trimmingCharacters(in: .whitespacesAndNewlines)
                }
            }
        } message: {
            Text("Enter a new name for this game session.")
        }
    }
    
    // MARK: - Computed Properties
    
    /// Get the game result text from PGN tags or game status
    private var gameResultText: String {
        // First try to get result from PGN tags
        let pgnResult = session.game.tags.result
        if !pgnResult.isEmpty && pgnResult != "*" {
            return pgnResult
        }
        return ""
    }
}

#Preview {
    ChessView()
}
